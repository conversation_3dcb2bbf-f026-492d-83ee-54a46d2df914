package grpcclient

import (
	"context"
	"time"

	intent "fin_gateway/proto"

	"google.golang.org/grpc"
)

type IntentClient struct {
	conn   *grpc.ClientConn
	client intent.IntentDetectorClient
}

func New(addr string) (*IntentClient, error) {
	conn, err := grpc.Dial(addr, grpc.WithInsecure())
	if err != nil {
		return nil, err
	}
	return &IntentClient{
		conn:   conn,
		client: intent.NewIntentDetectorClient(conn),
	}, nil
}

func (c *IntentClient) Detect(sessionID, text string) (*intent.IntentResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	return c.client.Detect(ctx, &intent.IntentRequest{
		SessionId: sessionID,
		Text:      text,
	})
}

func (c *IntentClient) Close() {
	c.conn.Close()
}
