package websocket

import (
	"encoding/json"
	"net/http"
	"sync"

	"fin_gateway/internal/grpcclient"
	"fin_gateway/internal/rabbitmq"

	"github.com/gorilla/websocket"
	"go.uber.org/zap"
)

type Server struct {
	upgrader   websocket.Upgrader
	clients    map[string]*websocket.Conn
	mu         sync.Mutex
	logger     *zap.Logger
	publisher  *rabbitmq.Publisher
	intentGrpc *grpcclient.IntentClient
}

func NewServer(logger *zap.Logger, pub *rabbitmq.Publisher, ic *grpcclient.IntentClient) *Server {
	return &Server{
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool { return true },
		},
		clients:    make(map[string]*websocket.Conn),
		logger:     logger,
		publisher:  pub,
		intentGrpc: ic,
	}
}

type IncomingMsg struct {
	SessionID string `json:"session_id"`
	Text      string `json:"text"`
}

func (s *Server) HandleWS(w http.ResponseWriter, r *http.Request) {
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		s.logger.Error("ws upgrade failed", zap.Error(err))
		return
	}
	defer conn.Close()

	for {
		_, msg, err := conn.ReadMessage()
		if err != nil {
			s.logger.Error("read ws msg failed", zap.Error(err))
			break
		}

		var in IncomingMsg
		if err := json.Unmarshal(msg, &in); err != nil {
			s.logger.Error("invalid msg", zap.Error(err))
			continue
		}

		// gRPC call to detect intent
		intentResp, err := s.intentGrpc.Detect(in.SessionID, in.Text)
		if err != nil {
			s.logger.Error("grpc detect failed", zap.Error(err))
			continue
		}

		out, _ := json.Marshal(map[string]interface{}{
			"session_id": in.SessionID,
			"text":       in.Text,
			"intent":     intentResp.Intent,
			"confidence": intentResp.Confidence,
		})

		// Publish to RabbitMQ
		if err := s.publisher.Publish("chat.input", out); err != nil {
			s.logger.Error("publish failed", zap.Error(err))
		}
	}
}
