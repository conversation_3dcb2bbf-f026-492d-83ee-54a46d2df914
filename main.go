package main

import (
	"context"
	"fin_gateway/internal/config"
	"fin_gateway/internal/grpcclient"
	"fin_gateway/internal/rabbitmq"
	"fin_gateway/internal/websocket"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

// ANSI colors
const (
	colorReset  = "\033[0m"
	colorRed    = "\033[31m"
	colorGreen  = "\033[32m"
	colorYellow = "\033[33m"
	colorBlue   = "\033[34m"
)

// pretty logger
func logInfo(msg string) {
	fmt.Printf("%s[INFO]%s  %s | %s\n", colorGreen, colorReset, time.Now().Format("2006-01-02 15:04:05"), msg)
}

func logWarn(msg string) {
	fmt.Printf("%s[WARN]%s  %s | %s\n", colorYellow, colorReset, time.Now().Format("2006-01-02 15:04:05"), msg)
}

func logError(msg string, err error) {
	fmt.Printf("%s[ERR ]%s  %s | %s : %v\n", colorRed, colorReset, time.Now().Format("2006-01-02 15:04:05"), msg, err)
}

func main() {
	// Load config
	cfg := config.Load()

	// Init RabbitMQ publisher
	pub, err := rabbitmq.NewPublisher(cfg.RabbitURL, nil)
	if err != nil {
		logError("failed to init publisher", err)
		os.Exit(1)
	}
	defer pub.Close()

	// Init gRPC client
	intentClient, err := grpcclient.New(cfg.GRPCAddr)
	if err != nil {
		logError("failed to init grpc client", err)
		os.Exit(1)
	}
	defer intentClient.Close()

	// Init WebSocket server
	wsServer := websocket.NewServer(nil, pub, intentClient)

	// HTTP server
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", wsServer.HandleWS)

	srv := &http.Server{
		Addr:    cfg.WSAddr,
		Handler: mux,
	}

	// Run server
	go func() {
		logInfo(fmt.Sprintf("starting %s at %s", cfg.ServiceName, cfg.WSAddr))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logError("server crashed", err)
		}
	}()

	// Graceful shutdown
	stop := make(chan os.Signal, 1)
	signal.Notify(stop, syscall.SIGINT, syscall.SIGTERM)

	<-stop
	logWarn("shutdown signal received, stopping gracefully...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logError("server forced to shutdown", err)
	} else {
		logInfo("server stopped cleanly")
	}

	logInfo(fmt.Sprintf("%s exited", cfg.ServiceName))
}
